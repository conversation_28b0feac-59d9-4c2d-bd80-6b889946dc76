// Background script for YouTube Study Extension

console.log('YouTube Study Extension background script loaded');

// ==================== 常量定义 ====================
const API_BASE_URL = 'http://localhost:8080/api';
const SESSION_TIMEOUT = 2 * 60 * 60 * 1000; // 2小时

// ==================== 状态管理 ====================
let userLoginState = {
    isLoggedIn: false,
    userInfo: null,
    accessToken: null,
    loginTime: null
};

let transcriptionTasks = {};
let activeSessions = {}; // 格式: { subtitleFileId: tabId }

// ==================== 通用工具函数 ====================
async function apiCall(endpoint, options = {}) {
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    const defaultHeaders = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
    };
    
    const config = {
        method: 'GET',
        headers: { ...defaultHeaders, ...options.headers },
        credentials: 'include',
        ...options
    };
    
    // 根据Content-Type智能处理请求体
    if (options.body) {
        const contentType = config.headers['Content-Type'];
        if (contentType && contentType.includes('application/x-www-form-urlencoded')) {
            config.body = options.body; // 对于form-urlencoded，body已经是字符串
        } else if (typeof options.body === 'object') {
            config.body = JSON.stringify(options.body); // 对于JSON，进行stringify
        } else {
            config.body = options.body; // 其他情况，直接使用
        }
    } else {
        config.body = null;
    }
    
    console.log(`API调用: ${config.method} ${url}`);
    
    try {
        const response = await fetch(url, config);
        console.log(`API响应: ${response.status}`);
        
            if (!response.ok) {
                // 尝试获取错误详情
                let errorMessage = `HTTP错误! 状态码: ${response.status}`;
                try {
                    const errorData = await response.text();
                    if (errorData) {
                        errorMessage += ` - ${errorData}`;
                    }
                } catch (e) {
                    // 忽略解析错误的异常
                }
                throw new Error(errorMessage);
            }
            
        // 根据响应类型处理数据
        if (options.responseType === 'text') {
            return await response.text();
        } else if (options.responseType === 'arrayBuffer') {
            const buffer = await response.arrayBuffer();
                    const bytes = new Uint8Array(buffer);
                    let binary = '';
                    for (let i = 0; i < bytes.byteLength; i++) {
                        binary += String.fromCharCode(bytes[i]);
                    }
            return btoa(binary);
            } else {
            return await response.json();
        }
    } catch (error) {
        console.error(`API调用失败: ${url}`, error);
        throw error;
    }
}

function saveActiveSessions(subtitleFileId, tabId) {
    if (subtitleFileId) {
        activeSessions[subtitleFileId] = tabId || 'unknown';
        console.log('保存活动会话:', subtitleFileId);
    }
}

function removeActiveSession(subtitleFileId) {
    if (subtitleFileId && activeSessions[subtitleFileId]) {
        delete activeSessions[subtitleFileId];
        console.log('移除活动会话:', subtitleFileId);
    }
}

// ==================== 会话管理 ====================
async function validateUserSession() {
    try {
        if (!userLoginState.isLoggedIn || !userLoginState.accessToken) {
            await restoreUserStateAsync();
        }
        
        if (!userLoginState.isLoggedIn || !userLoginState.accessToken) {
            return await validateBackendSession();
        }
        
        // 检查登录时间是否过期
        const currentTime = new Date().getTime();
        const loginTime = userLoginState.loginTime;
        
        if (currentTime - loginTime > SESSION_TIMEOUT) {
            console.log('登录时间已超过2小时，认为会话过期');
            userLoginState.isLoggedIn = false;
            return false;
        }
        
        const backendValid = await validateBackendSession();
        
        if (!backendValid && userLoginState.accessToken) {
            return await reauthenticateWithToken();
        }
        
        return backendValid;
    } catch (error) {
        console.error('验证会话时出错:', error);
        return false;
    }
}

async function validateBackendSession() {
    try {
        const data = await apiCall('/auth/preauth');
        return data.authenticated === true;
    } catch (error) {
        console.error('后端会话验证出错:', error);
        return false;
    }
}

async function restoreUserStateAsync() {
    return new Promise((resolve) => {
        chrome.storage.sync.get(['userInfo', 'accessToken', 'loginTime'], (result) => {
            if (result.userInfo && result.accessToken) {
                console.log('异步恢复用户登录状态:', result.userInfo.email);
                userLoginState = {
                    isLoggedIn: true,
                    userInfo: result.userInfo,
                    accessToken: result.accessToken,
                    loginTime: result.loginTime || new Date().getTime()
                };
            }
            resolve();
        });
    });
}

async function reauthenticateWithToken() {
    try {
        await apiCall('/auth/token-login', {
            method: 'POST',
            body: JSON.stringify({ token: userLoginState.accessToken })
        });
        return true;
    } catch (error) {
        console.error('Token重新认证出错:', error);
        userLoginState.isLoggedIn = false;
        return false;
    }
}

// ==================== API调用函数 ====================
async function fetchSubtitlesFromBackend(videoUrl, retryCount = 0, options = {}) {
        const requestBody = {
            videoUrl: videoUrl,
        timestamp: new Date().getTime()
        };
        
        if (options.forceNewSession) {
            requestBody.forceNewSession = true;
            console.log('请求强制使用新会话');
        }
        
        // 添加语言参数支持
        if (options.language) {
            requestBody.language = options.language;
            console.log('请求语言:', options.language);
        }
        
    const data = await apiCall('/extension/subtitles/fetch', {
            method: 'POST',
        body: JSON.stringify(requestBody)
    });
        
        if (data.success) {
        console.log('获取字幕成功:', data.videoId, data.subtitleFileId);
            return {
                success: true,
                videoId: data.videoId,
            subtitleFileId: data.subtitleFileId,
                subtitleJa: data.subtitleJa || '',
                subtitleZh: data.subtitleZh || '',
                warning: data.warning || null
            };
        } else {
            throw new Error(data.error || '服务器返回失败状态');
    }
}

async function cleanupSubtitlesFromBackend(subtitleFileId) {
        if (!subtitleFileId) {
            throw new Error('subtitleFileId不能为空');
        }
        
    const data = await apiCall(`/extension/subtitles/cleanup?subtitleFileId=${encodeURIComponent(subtitleFileId)}`, {
        method: 'DELETE'
    });
        
        return {
            success: data.success,
            message: data.message || '清理完成'
        };
}

// 转录视频
async function transcribeVideo(videoUrl, language, subtitleFileId) {
    const requestBody = { videoUrl, language };
    if (subtitleFileId) {
        requestBody.subtitleFileId = subtitleFileId;
    }
    
    try {
        const data = await apiCall('/extension/transcribe', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });
            
        if (data.success) {
            return {
                success: true,
                taskId: data.taskId,
                message: data.message || '转录请求已提交'
            };
        } else {
            throw new Error(data.error || '服务器返回失败状态');
        }
    } catch (error) {
        console.error('转录API调用失败:', error);
        
        // 处理HTTP状态码错误
        if (error.message.includes('403')) {
            // 403 Forbidden - Premium会员权限问题
            return { 
                success: false, 
                error: 'Premium会员权限不足', 
                data: { error: 'PREMIUM_REQUIRED' } 
            };
        } else if (error.message.includes('401')) {
            // 401 Unauthorized - 认证问题
            return { 
                success: false, 
                error: '用户认证已过期，请重新登录', 
                data: { error: 'AUTHENTICATION_REQUIRED' } 
            };
        }
        
        throw error;
    }
}

async function getTranscriptionProgress(taskId) {
        if (!taskId) {
            throw new Error('taskId不能为空');
        }
        
    const data = await apiCall(`/extension/transcribe/progress?taskId=${encodeURIComponent(taskId)}`);
        
        if (data.success) {
            // 更新本地存储的任务状态
            if (transcriptionTasks[taskId]) {
            Object.assign(transcriptionTasks[taskId], {
                status: data.status,
                progress: data.progress,
                currentStep: data.currentStep
            });
            }
            return data;
        } else {
            throw new Error(data.error || '服务器返回失败状态');
        }
}

// 生成视频摘要
async function generateVideoSummary(subtitleFileId, videoUrl, subtitleContent = null) {
    if (!subtitleFileId && !subtitleContent) {
        throw new Error('字幕文件ID和字幕内容不能同时为空');
    }

    try {
        const requestBody = { subtitleFileId, videoUrl };

        // 如果有字幕内容，添加到请求体中
        if (subtitleContent) {
            requestBody.subtitleContent = subtitleContent;
            console.log('包含字幕内容的摘要请求，内容大小:', subtitleContent.length);
        }

        const data = await apiCall('/summary/generate', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });
        
        if (data.success) {
            return {
                success: true,
                summary: data.summary,
                videoTitle: data.videoTitle
            };
        } else {
            throw new Error(data.message || '生成摘要失败');
        }
    } catch (error) {
        console.error('摘要API调用失败:', error);
        
        // 处理HTTP状态码错误
        if (error.message.includes('403')) {
            // 403 Forbidden - Premium会员权限问题
            return { 
                success: false, 
                error: 'Premium会员权限不足', 
                data: { error: 'PREMIUM_REQUIRED' } 
            };
        } else if (error.message.includes('401')) {
            // 401 Unauthorized - 认证问题
            return { 
                success: false, 
                error: '用户认证已过期，请重新登录', 
                data: { error: 'AUTHENTICATION_REQUIRED' } 
            };
        }
        
        throw error;
    }
}

// 生成思维导图
async function generateMindMap(subtitleFileId, videoUrl, subtitleContent = null) {
    if (!subtitleFileId && !subtitleContent) {
        throw new Error('字幕文件ID和字幕内容不能同时为空');
    }

    try {
        const requestBody = { subtitleFileId, videoUrl };

        // 如果有字幕内容，添加到请求体中
        if (subtitleContent) {
            requestBody.subtitleContent = subtitleContent;
            console.log('包含字幕内容的思维导图请求，内容大小:', subtitleContent.length);
        }

        // API现在成功时直接返回mindmap数据，失败时返回带error属性的对象
        const data = await apiCall('/mindmap/generate', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });
        
        // 如果响应中包含 'id' 和 'children'，我们认为它是成功的mindmap数据
        if (data && data.id && data.children) {
            return data;
        } 
        // 否则，它应该是一个错误对象
        else if (data && data.error) {
            throw new Error(data.error); // 抛出错误，由catch块处理
        }
        // 处理未知格式的成功响应
        else {
            throw new Error('UNEXPECTED_RESPONSE_FORMAT');
        }

    } catch (error) {
        console.error('思维导图API调用或处理失败:', error);
        
        // 从错误消息中提取关键信息
        const errorMessage = error.message || 'UNKNOWN_ERROR';

        if (errorMessage.includes('403') || errorMessage.includes('PLUS_OR_PREMIUM_REQUIRED')) {
            return { error: 'PLUS_OR_PREMIUM_REQUIRED', message: 'Plus或Premium会员权限不足' };
        } else if (errorMessage.includes('401') || errorMessage.includes('AUTHENTICATION_REQUIRED')) {
            return { error: 'AUTHENTICATION_REQUIRED', message: '用户认证已过期' };
        } else if (errorMessage.includes('UNEXPECTED_RESPONSE_FORMAT')) {
             return { error: 'UNEXPECTED_RESPONSE_FORMAT', message: '从服务器返回的响应格式不正确' };
        }
        
        // 其他所有未捕获的错误
        return { error: 'INTERNAL_ERROR', message: `生成思维导图时发生内部错误: ${errorMessage}` };
    }
}

async function chatAsk(sessionId, question, subtitleFileId) {
    if (!sessionId || !question) {
        throw new Error('sessionId和question不能为空');
    }
    
    // 验证会话状态
    const isSessionValid = await validateUserSession();
    if (!isSessionValid) {
        throw new Error('用户认证已过期，请重新登录');
    }
    
    try {
        const data = await apiCall('/chat/ask', {
            method: 'POST',
            body: JSON.stringify({ sessionId, question, subtitleFileId })
        });
        
        return { success: true, data };
    } catch (error) {
        console.error('聊天API调用失败:', error);
        
        // 处理HTTP状态码错误
        if (error.message.includes('403')) {
            // 403 Forbidden - 可能是Premium会员权限问题
            return { 
                success: false, 
                error: 'Premium会员权限不足', 
                data: { error: 'PREMIUM_REQUIRED' } 
            };
        } else if (error.message.includes('401')) {
            // 401 Unauthorized - 认证问题
            // 尝试重新认证
            const reauthSuccess = await validateUserSession();
            if (reauthSuccess) {
                try {
                    const data = await apiCall('/chat/ask', {
                        method: 'POST',
                        body: JSON.stringify({ sessionId, question, subtitleFileId })
                    });
                    return { success: true, data };
                } catch (retryError) {
                    console.error('重新认证后仍然失败:', retryError);
                    return { 
                        success: false, 
                        error: '认证失败，请重新登录', 
                        data: { error: 'AUTHENTICATION_REQUIRED' } 
                    };
                }
            } else {
                return { 
                    success: false, 
                    error: '用户认证失败，请重新登录', 
                    data: { error: 'AUTHENTICATION_REQUIRED' } 
                };
            }
        } else if (error.message.includes('302')) {
            // 302 重定向 - 通常也是认证问题
            const reauthSuccess = await validateUserSession();
            if (reauthSuccess) {
                try {
                    const data = await apiCall('/chat/ask', {
                        method: 'POST',
                        body: JSON.stringify({ sessionId, question, subtitleFileId })
                    });
                    return { success: true, data };
                } catch (retryError) {
                    return { 
                        success: false, 
                        error: '认证失败，请重新登录', 
                        data: { error: 'AUTHENTICATION_REQUIRED' } 
                    };
                }
            } else {
                return { 
                    success: false, 
                    error: '用户认证失败，请重新登录', 
                    data: { error: 'AUTHENTICATION_REQUIRED' } 
                };
            }
        }
        
        // 其他错误
        return { 
            success: false, 
            error: error.message || '聊天请求失败', 
            data: { error: 'PROCESSING_FAILED' } 
        };
    }
}

async function chatClear(sessionId) {
    if (!sessionId) {
        throw new Error('sessionId不能为空');
    }
    
    try {
        const data = await apiCall('/chat/clear', {
            method: 'POST',
            body: JSON.stringify({ sessionId })
        });
        return { success: true, data };
    } catch (error) {
        console.warn('清除聊天会话失败，但不影响退出流程:', error);
        return { success: false, data: { message: '清除失败，但本地会话已清除' } };
    }
}

// ==================== 弹幕相关API函数 ====================
async function sendDanmaku(videoId, content, videoTimestamp, color, type) {
    console.log('发送弹幕请求:', { videoId, content, videoTimestamp, color, type });
    
    if (!videoId || !content || videoTimestamp == null) {
        throw new Error('videoId、content和videoTimestamp不能为空');
    }
    
    // 验证会话状态
    console.log('验证用户会话...');
    const isSessionValid = await validateUserSession();
    console.log('会话验证结果:', isSessionValid);
    
    if (!isSessionValid) {
        throw new Error('用户认证已过期，请重新登录');
    }
    
    try {
        const requestBody = {
            videoId: videoId.trim(),
            content: content.trim(),
            videoTimestamp: videoTimestamp
        };
        
        // 添加可选参数
        if (color) {
            requestBody.color = color;
        }
        if (type) {
            requestBody.type = type;
        }
        
        const data = await apiCall('/danmaku/send', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });
        
        return { success: true, data };
    } catch (error) {
        // 如果认证失败，尝试重新认证后重试
        if (error.message.includes('401') || error.message.includes('302')) {
            const reauthSuccess = await validateUserSession();
            if (reauthSuccess) {
                const requestBody = {
                    videoId: videoId.trim(),
                    content: content.trim(),
                    videoTimestamp: videoTimestamp
                };
                
                if (color) requestBody.color = color;
                if (type) requestBody.type = type;
                
                const data = await apiCall('/danmaku/send', {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });
                return { success: true, data };
            } else {
                throw new Error('用户认证失败，请重新登录');
            }
        }
        throw error;
    }
}

async function getDanmakuList(videoId, page = 0, size = 100) {
    if (!videoId) {
        throw new Error('videoId不能为空');
    }
    
    const params = new URLSearchParams({
        videoId: videoId.trim(),
        page: page.toString(),
        size: size.toString()
    });
    
    const data = await apiCall(`/danmaku/list?${params.toString()}`);
    return { success: true, data };
}

async function getDanmakuByTimeRange(videoId, startTime, endTime) {
    if (!videoId || startTime == null || endTime == null) {
        throw new Error('videoId、startTime和endTime不能为空');
    }
    
    const params = new URLSearchParams({
        videoId: videoId.trim(),
        startTime: startTime.toString(),
        endTime: endTime.toString()
    });
    
    const data = await apiCall(`/danmaku/range?${params.toString()}`);
    return { success: true, data };
}

async function getDanmakuStats(videoId) {
    if (!videoId) {
        throw new Error('videoId不能为空');
    }
    
    const params = new URLSearchParams({
        videoId: videoId.trim()
    });
    
    const data = await apiCall(`/danmaku/stats?${params.toString()}`);
    return { success: true, data };
}

async function deleteDanmaku(danmakuId) {
    if (!danmakuId) {
        throw new Error('danmakuId不能为空');
    }
    
    // 验证会话状态
    const isSessionValid = await validateUserSession();
    if (!isSessionValid) {
        throw new Error('用户认证已过期，请重新登录');
    }
    
    try {
        const data = await apiCall(`/danmaku/${danmakuId}`, {
            method: 'DELETE'
        });
        return { success: true, data };
    } catch (error) {
        // 如果认证失败，尝试重新认证后重试
        if (error.message.includes('401') || error.message.includes('302')) {
            const reauthSuccess = await validateUserSession();
            if (reauthSuccess) {
                const data = await apiCall(`/danmaku/${danmakuId}`, {
                    method: 'DELETE'
                });
                return { success: true, data };
            } else {
                throw new Error('用户认证失败，请重新登录');
            }
        }
        throw error;
    }
}

// ==================== Premium权限检查 ====================

// 检查Premium会员权限
async function checkPremiumPermission() {
    try {
        // 验证会话状态
        const isSessionValid = await validateUserSession();
        if (!isSessionValid) {
            return {
                success: false,
                isPremium: false,
                error: '用户认证已过期，请重新登录',
                errorType: 'AUTHENTICATION_REQUIRED'
            };
        }
        
        // 调用后端API检查Premium权限
        const data = await apiCall('/subscription/active', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        // 检查是否有活跃订阅且为Premium会员
        const hasActiveSubscription = data.hasActiveSubscription || false;
        let isPremium = false;
        
        if (hasActiveSubscription && data.subscription) {
            const subscription = data.subscription;
            const planName = subscription.planName || '';
            const planId = subscription.planId || '';
            
            // 检查是否为Premium会员（包括试用版）
            isPremium = (planName.toLowerCase().includes('premium')) ||
                       (planId.toLowerCase().includes('premium')) ||
                       (planId.toLowerCase().includes('trial_premium'));
        }
        
        console.log('Premium权限检查结果:', {
            authenticated: data.authenticated,
            hasActiveSubscription,
            isPremium,
            subscription: data.subscription
        });
        
        return {
            success: true,
            isPremium: isPremium,
            hasActiveSubscription: hasActiveSubscription,
            subscription: data.subscription
        };
        
    } catch (error) {
        console.error('检查Premium权限失败:', error);
        
        // 处理HTTP状态码错误
        if (error.message.includes('401') || error.message.includes('403')) {
            return {
                success: false,
                isPremium: false,
                error: '用户认证已过期，请重新登录',
                errorType: 'AUTHENTICATION_REQUIRED'
            };
        }
        
        return {
            success: false,
            isPremium: false,
            error: '权限检查失败: ' + error.message,
            errorType: 'CHECK_FAILED'
        };
    }
}

// 检查Plus或Premium会员权限
async function checkPlusOrPremiumPermission() {
    try {
        // 验证会话状态
        const isSessionValid = await validateUserSession();
        if (!isSessionValid) {
            return {
                success: false,
                isPlusOrPremium: false,
                error: '用户认证已过期，请重新登录',
                errorType: 'AUTHENTICATION_REQUIRED'
            };
        }
        
        // 调用后端API检查Plus/Premium权限
        const data = await apiCall('/subscription/active', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        // 检查是否有活跃订阅且为Plus或Premium会员
        const hasActiveSubscription = data.hasActiveSubscription || false;
        let isPlusOrPremium = false;
        
        if (hasActiveSubscription && data.subscription) {
            const subscription = data.subscription;
            const planName = subscription.planName || '';
            const planId = subscription.planId || '';
            
            // 检查是否为Plus会员或Premium会员（包括试用版）
            isPlusOrPremium = (planName.toLowerCase().includes('plus')) ||
                             (planName.toLowerCase().includes('premium')) ||
                             (planId.toLowerCase().includes('plus')) ||
                             (planId.toLowerCase().includes('premium')) ||
                             (planId.toLowerCase().includes('trial_premium'));
        }
        
        console.log('Plus/Premium权限检查结果:', {
            authenticated: data.authenticated,
            hasActiveSubscription,
            isPlusOrPremium,
            subscription: data.subscription
        });
        
        return {
            success: true,
            isPlusOrPremium: isPlusOrPremium,
            hasActiveSubscription: hasActiveSubscription,
            subscription: data.subscription
        };
        
    } catch (error) {
        console.error('检查Plus/Premium权限失败:', error);
        
        // 处理HTTP状态码错误
        if (error.message.includes('401') || error.message.includes('403')) {
            return {
                success: false,
                isPlusOrPremium: false,
                error: '用户认证已过期，请重新登录',
                errorType: 'AUTHENTICATION_REQUIRED'
            };
        }
        
        return {
            success: false,
            isPlusOrPremium: false,
            error: '权限检查失败: ' + error.message,
            errorType: 'CHECK_FAILED'
        };
    }
}

// ==================== 消息处理函数 ====================
async function handleFetchSubtitles(request, sender) {
    const response = await fetchSubtitlesFromBackend(request.videoUrl, 0, {
        forceNewSession: request.forceNewSession,
        timestamp: request.timestamp,
        language: request.language // 传递语言参数
    });
    
    if (response.subtitleFileId) {
        saveActiveSessions(response.subtitleFileId, sender.tab?.id);
    }
    
    return response;
}

async function handleCleanupSubtitles(request) {
    console.log('开始清理字幕文件:', request.subtitleFileId);
    
    try {
        if (!request.subtitleFileId) {
            return { success: false, message: 'subtitleFileId 不能为空' };
        }
        
        // 调用统一的清理接口，该接口现在负责清理yt-dlp和转录文件
        const cleanupResponse = await cleanupSubtitlesFromBackend(request.subtitleFileId);
        console.log('统一清理结果:', cleanupResponse);
        
        // 移除活动会话
        removeActiveSession(request.subtitleFileId);
        
        return {
            success: cleanupResponse.success,
            message: cleanupResponse.message,
            details: cleanupResponse // 详细信息直接来自后端
        };
        
    } catch (error) {
        console.error('清理字幕文件时发生错误:', error);
        
        // 即使清理失败，也要移除活动会话
        if (request.subtitleFileId) {
            removeActiveSession(request.subtitleFileId);
        }
        
        return {
            success: false,
            message: '清理字幕文件时发生错误: ' + error.message
        };
    }
}

async function handleTranscribeVideo(request) {
    const response = await transcribeVideo(request.videoUrl, request.language, request.subtitleFileId);
    
    if (response.success && response.taskId) {
        transcriptionTasks[response.taskId] = {
            videoUrl: request.videoUrl,
            language: request.language,
            startTime: Date.now(),
            status: 'pending',
            progress: 0
        };
    }
    
    return response;
}

async function handleUserLogin(request) {
    console.log('用户已登录:', request.userInfo);
    userLoginState = {
        isLoggedIn: true,
        userInfo: request.userInfo,
        accessToken: request.accessToken,
        loginTime: new Date().getTime()
    };
    return { success: true };
}

async function handleUserLogout() {
    console.log('用户已登出');
    userLoginState = {
        isLoggedIn: false,
        userInfo: null,
        accessToken: null,
        loginTime: null
    };
    return { success: true };
}

async function handleValidateSession() {
    const isValid = await validateUserSession();
    return {
        isValid: isValid,
        userInfo: isValid ? userLoginState.userInfo : null
    };
}

async function handleProxyFetch(request) {
    const isTTSRequest = request.url.includes('/api/tts/speak');
    const isSubtitleRequest = request.url.includes('.vtt') || 
                            request.url.includes('/subtitles/') ||
                            (request.url.includes('/api/extension/transcribe') && request.method === 'GET');
    
    let responseType = 'json';
    if (isTTSRequest) responseType = 'arrayBuffer';
    if (isSubtitleRequest) responseType = 'text';
    
    const data = await apiCall(request.url, {
        method: request.method || 'GET',
        headers: request.headers || {},
        body: request.body || null,
        responseType
    });
    
    return { success: true, data };
}

async function handleSessionExpired() {
    console.log('收到会话过期通知');
    
    userLoginState = {
        isLoggedIn: false,
        userInfo: null,
        accessToken: null,
        loginTime: null
    };
    
    // 向所有标签页发送会话过期通知
    chrome.tabs.query({}, function(tabs) {
        tabs.forEach(tab => {
            try {
                chrome.tabs.sendMessage(tab.id, {action: 'sessionExpired'});
            } catch (e) {
                console.warn(`向标签页 ${tab.id} 发送会话过期通知失败:`, e);
            }
        });
    });
    
    chrome.notifications.create({
        type: 'basic',
        iconUrl: 'images/icon48.png',
        title: 'YouTube学习插件',
        message: '会话已过期，请重新登录'
    });
    
    return { success: true };
}

async function handleOpenUpgradePage(url) {
    console.log('打开升级页面请求:', url);
    if (url) {
        chrome.tabs.create({ url: url });
    } else {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'YouTube学习插件',
            message: '无法打开升级页面，请手动访问'
        });
    }
    return { success: true };
}

// ==================== 消息路由 ====================
const messageHandlers = {
    'fetchExtensionSubtitles': handleFetchSubtitles,
    'cleanupExtensionSubtitles': handleCleanupSubtitles,
    'transcribeVideo': handleTranscribeVideo,
    'getTranscriptionProgress': (request) => getTranscriptionProgress(request.taskId),
    'userLoggedIn': handleUserLogin,
    'userLoggedOut': handleUserLogout,
    'validateSession': handleValidateSession,
    'proxyFetch': handleProxyFetch,
    'notifySessionExpired': handleSessionExpired,
    'generateVideoSummary': (request) => generateVideoSummary(request.subtitleFileId, request.videoUrl, request.subtitleContent),
    'generateMindMap': (request) => generateMindMap(request.subtitleFileId, request.videoUrl, request.subtitleContent),
    'chatAsk': (request) => chatAsk(request.sessionId, request.question, request.subtitleFileId),
    'chatClear': (request) => chatClear(request.sessionId),
    'openUpgradePage': (request) => handleOpenUpgradePage(request.url),
    'checkPremiumPermission': () => checkPremiumPermission(),
    'checkPlusOrPremiumPermission': () => checkPlusOrPremiumPermission(),
    // 弹幕相关消息处理
    'sendDanmaku': (request) => sendDanmaku(request.videoId, request.content, request.videoTimestamp, request.color, request.type),
    'getDanmakuList': (request) => getDanmakuList(request.videoId, request.page, request.size),
    'getDanmakuByTimeRange': (request) => getDanmakuByTimeRange(request.videoId, request.startTime, request.endTime),
    'getDanmakuStats': (request) => getDanmakuStats(request.videoId),
    'deleteDanmaku': (request) => deleteDanmaku(request.danmakuId)
};

// ==================== 事件监听器 ====================
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background收到消息:', request.action);
    
    const handler = messageHandlers[request.action];
    if (handler) {
        handler(request, sender)
            .then(response => sendResponse(response))
            .catch(error => {
                console.error(`处理${request.action}失败:`, error);
                sendResponse({
                    success: false,
                    error: error.message || '处理请求时发生未知错误'
                });
            });
        return true; // 保持消息通道开放用于异步响应
    }
});

chrome.action.onClicked.addListener((tab) => {
    console.log('插件图标被点击, tab:', tab.url);
    
    if (tab.url && tab.url.includes('youtube.com/watch')) {
        chrome.tabs.sendMessage(tab.id, { action: 'toggleStudyMode' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('发送消息失败:', chrome.runtime.lastError.message);
            }
        });
    } else {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'YouTube学习插件',
            message: '请在YouTube视频页面使用此插件'
        });
    }
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com/watch')) {
        console.log('检测到YouTube视频页面加载完成:', tab.url);
        
        chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('Content script未注入，正在注入...');
                try {
                    if (chrome.scripting && chrome.scripting.executeScript) {
                        chrome.scripting.executeScript({
                            target: { tabId: tabId },
                            files: ['content.js']
                        }).catch(error => console.error('Content script注入失败:', error));
                    } else if (chrome.tabs && chrome.tabs.executeScript) {
                        chrome.tabs.executeScript(tabId, { file: 'content.js' }, () => {
                            if (chrome.runtime.lastError) {
                                console.error('Content script注入失败:', chrome.runtime.lastError);
                            }
                        });
                    }
                } catch (error) {
                    console.error('脚本注入出错:', error);
                }
            }
        });
    }
});

chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    console.log('标签页关闭:', tabId);
    
    const sessionsToCleanup = [];
    for (const [fileId, sessionTabId] of Object.entries(activeSessions)) {
        if (sessionTabId === tabId) {
            sessionsToCleanup.push(fileId);
        }
    }
    
    if (sessionsToCleanup.length > 0) {
        console.log('需要清理的会话:', sessionsToCleanup);
        sessionsToCleanup.forEach(fileId => {
            cleanupSubtitlesFromBackend(fileId)
                .then(() => {
                    console.log('标签关闭时清理字幕成功:', fileId);
                    removeActiveSession(fileId);
                })
                .catch(error => console.error('标签关闭时清理字幕失败:', fileId, error));
        });
    }
});

chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
});

chrome.runtime.onStartup.addListener(() => {
    console.log('插件启动，恢复用户状态');
    restoreUserState();
});

chrome.runtime.onSuspend.addListener(() => {
    console.log('Background script即将被挂起');
    
    const filesToCleanup = Object.keys(activeSessions);
    if (filesToCleanup.length > 0) {
        console.log('浏览器关闭前清理所有字幕文件:', filesToCleanup);
        filesToCleanup.forEach(fileId => {
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('DELETE', `${API_BASE_URL}/extension/subtitles/cleanup?subtitleFileId=${fileId}`, false);
                xhr.send();
                console.log('同步清理字幕文件结果:', fileId, xhr.status);
            } catch (e) {
                console.error('同步清理字幕文件失败:', fileId, e);
            }
        });
    }
});

// ==================== 初始化 ====================
function restoreUserState() {
    chrome.storage.sync.get(['userInfo', 'accessToken', 'loginTime'], (result) => {
        if (result.userInfo && result.accessToken) {
            console.log('恢复用户登录状态:', result.userInfo.email);
            userLoginState = {
                isLoggedIn: true,
                userInfo: result.userInfo,
                accessToken: result.accessToken,
                loginTime: result.loginTime || new Date().getTime()
            };
        }
    });
}

// 错误处理
self.addEventListener('error', (event) => {
    console.error('Background script错误:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Background script未处理的Promise拒绝:', event.reason);
});

// 启动时恢复用户状态
restoreUserState();

async function cleanupTranscriptionFilesFromBackend(videoId, taskId) {
    if (!videoId) {
        throw new Error('videoId不能为空');
    }
    
    let url = `/extension/transcription/cleanup?videoId=${encodeURIComponent(videoId)}`;
    if (taskId) {
        url += `&taskId=${encodeURIComponent(taskId)}`;
    }
    
    const data = await apiCall(url, {
        method: 'DELETE'
    });
    
    return {
        success: data.success,
        message: data.message || '转录文件清理完成'
    };
}
