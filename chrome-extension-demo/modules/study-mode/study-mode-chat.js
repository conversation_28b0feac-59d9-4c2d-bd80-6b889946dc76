// Study Mode Chat Module - 聊天功能模块
console.log('Study Mode Chat module loaded');

/**
 * 聊天UI创建
 */

// 创建聊天区域
function createChatArea(overlay) {
  console.log('创建聊天区域');
  
  // 检查是否已经存在聊天气泡
  let chatBubble = document.getElementById('study-mode-chat-bubble');
  if (chatBubble) {
    return;
  }
  
  // 创建聊天气泡
  chatBubble = document.createElement('div');
  chatBubble.id = 'study-mode-chat-bubble';
  chatBubble.className = 'study-mode-chat-bubble';
  chatBubble.innerHTML = `
    <div class="chat-bubble-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 9h8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 13h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div class="chat-bubble-text">AI助手</div>
  `;
  
  // 创建聊天容器（初始隐藏）
  let chatContainer = document.createElement('div');
  chatContainer.id = 'study-mode-chat-container';
  chatContainer.className = 'study-mode-chat-container hidden';
  
  // 创建聊天消息区域
  const chatMessages = document.createElement('div');
  chatMessages.id = 'chat-messages';
  chatMessages.className = 'chat-messages';
  
  // 创建聊天输入区域
  const chatInputArea = document.createElement('div');
  chatInputArea.className = 'chat-input-area';
  chatInputArea.innerHTML = `
    <div class="chat-input-container">
      <textarea class="chat-input" placeholder="请输入您的问题..." rows="1"></textarea>
      <button class="chat-send-btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
  `;
  
  // 组装聊天容器
  chatContainer.appendChild(chatMessages);
  chatContainer.appendChild(chatInputArea);
  
  // 添加到覆盖层
  overlay.appendChild(chatBubble);
  overlay.appendChild(chatContainer);
  
  // 设置聊天气泡点击事件
  setupChatBubbleEvents(chatBubble, chatContainer);
  
  // 设置输入事件
  setupChatInputEvents(chatContainer);
}

// 调整聊天容器高度以与播放器对齐
function adjustChatContainerHeight() {
  const chatContainer = document.getElementById('study-mode-chat-container');
  const playerContainer = document.getElementById('study-mode-player-container');
  
  if (!chatContainer || !playerContainer) return;
  
  // 获取播放器容器的实际高度和位置
  const playerRect = playerContainer.getBoundingClientRect();
  const playerHeight = playerRect.height;
  const playerTop = playerRect.top;
  
  // 计算聊天区域应该的高度（与播放器高度一致）
  const chatTop = 70; // 与CSS中的top值一致
  const maxChatHeight = playerHeight;
  
  // 设置聊天容器的高度
  chatContainer.style.height = `${maxChatHeight}px`;
  chatContainer.style.maxHeight = `${maxChatHeight}px`;
}

/**
 * 聊天事件处理
 */

// 设置聊天气泡事件
function setupChatBubbleEvents(chatBubble, chatContainer) {
  let isExpanded = false;
  
  chatBubble.addEventListener('click', function() {
    isExpanded = !isExpanded;
    
    if (isExpanded) {
      // 展开聊天区域
      chatContainer.classList.remove('hidden');
      chatContainer.classList.add('expanding');
      chatBubble.classList.add('active');
      
      // 动态调整聊天区域高度
      setTimeout(() => {
        adjustChatContainerHeight();
        chatContainer.classList.remove('expanding');
        
        // 检查是否需要添加欢迎消息
        const chatMessages = chatContainer.querySelector('.chat-messages');
        if (chatMessages && chatMessages.children.length === 0) {
          addWelcomeMessage(chatMessages);
        }
      }, 100);
    } else {
      // 收起聊天区域
      chatContainer.classList.add('collapsing');
      chatBubble.classList.remove('active');
      
      setTimeout(() => {
        chatContainer.classList.add('hidden');
        chatContainer.classList.remove('collapsing');
      }, 300);
    }
  });
}

// 设置聊天输入事件
function setupChatInputEvents(chatContainer) {
  const chatInput = chatContainer.querySelector('.chat-input');
  const chatSendBtn = chatContainer.querySelector('.chat-send-btn');
  
  // 自动调整输入框高度
  chatInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
  });
  
  // 发送按钮点击事件
  chatSendBtn.addEventListener('click', function() {
    sendMessage(chatContainer);
  });
}

/**
 * 消息处理
 */

// 发送消息
async function sendMessage(chatContainer) {
  const chatInput = chatContainer.querySelector('.chat-input');
  const message = chatInput.value.trim();
  
  if (!message) return;
  
  // 添加用户消息
  addMessage(chatContainer, message, 'user');
  
  // 清空输入框
  chatInput.value = '';
  chatInput.style.height = 'auto';
  
  // 禁用发送按钮，防止重复发送
  const sendBtn = chatContainer.querySelector('.chat-send-btn');
  sendBtn.disabled = true;
  
  // 添加loading消息
  const loadingMessageId = addLoadingMessage(chatContainer);
  
  try {
    // 调用后端API获取回答
    const aiResponse = await callChatAPI(message);
    
    // 移除loading消息
    removeLoadingMessage(chatContainer, loadingMessageId);
    
    // 添加AI回答
    addMessage(chatContainer, aiResponse, 'ai', true);
  } catch (error) {
    console.error('调用聊天API失败:', error);
    
    // 移除loading消息
    removeLoadingMessage(chatContainer, loadingMessageId);
    
    // 根据错误类型显示不同的提示
    if (error.isPremiumRequired) {
      // 显示Premium会员升级提示
      addPremiumUpgradeMessage(chatContainer);
    } else if (error.isAuthRequired) {
      // 显示登录提示
      addMessage(chatContainer, '请先登录后再使用AI助手功能。', 'ai');
    } else {
      // 显示通用错误消息
      addMessage(chatContainer, error.message || '抱歉，我暂时无法回答您的问题，请稍后再试。', 'ai');
    }
  } finally {
    // 重新启用发送按钮
    sendBtn.disabled = false;
  }
}

// 调用后端聊天API
async function callChatAPI(question) {
  try {
    // 获取当前的字幕文件ID
    const subtitleFileId = window.StudyModeUtils.getCurrentSubtitleFileId();
    
    // 生成或获取会话ID
    const sessionId = window.StudyModeSession.getOrCreateSessionId();
    
    // 使用background.js代理请求，避免跨域问题
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'chatAsk',
        sessionId: sessionId,
        question: question,
        subtitleFileId: subtitleFileId
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    
    if (response.success) {
      return response.data.answer;
    } else {
      // 根据错误类型创建不同的错误对象
      const error = new Error(response.error || '获取回答失败');
      
      // 检查是否为Premium会员权限错误
      if (response.data && response.data.error === 'PREMIUM_REQUIRED') {
        error.isPremiumRequired = true;
        error.message = 'AI助手功能仅限Premium会员使用，请升级您的会员等级';
      }
      // 检查是否为认证错误
      else if (response.data && response.data.error === 'AUTHENTICATION_REQUIRED') {
        error.isAuthRequired = true;
        error.message = '请先登录后再使用AI助手功能';
      }
      // 检查传统的认证错误格式（向后兼容）
      else if (response.error && response.error.includes('认证')) {
        error.isAuthRequired = true;
        error.message = '会话已过期，请刷新页面重新登录后再试';
      }
      
      throw error;
    }
  } catch (error) {
    console.error('调用聊天API失败:', error);
    throw error;
  }
}

/**
 * Premium会员升级提示
 */

// 添加Premium会员升级提示消息
function addPremiumUpgradeMessage(chatContainer) {
  const chatMessages = chatContainer.querySelector('.chat-messages');
  
  // 创建消息元素
  const messageElement = document.createElement('div');
  messageElement.className = 'chat-message ai';
  
  // 创建AI头像
  const avatar = document.createElement('div');
  avatar.className = 'chat-avatar ai';
  avatar.style.backgroundImage = `url(chrome-extension://${chrome.runtime.id}/assets/images/ai.png)`;
  avatar.style.backgroundSize = 'cover';
  avatar.style.backgroundPosition = 'center';
  avatar.style.backgroundRepeat = 'no-repeat';
  
  // 创建消息气泡
  const bubble = document.createElement('div');
  bubble.className = 'chat-bubble premium-upgrade-bubble';
  
  // 创建简单的文字提示内容
  const textContentSpan = document.createElement('span');
  textContentSpan.className = 'chat-text-content';
  textContentSpan.textContent = 'AI助手功能仅限Premium会员使用，请升级您的会员等级以享受完整功能。';
  
  // 创建升级链接按钮
  const upgradeLink = document.createElement('button');
  upgradeLink.className = 'upgrade-link-btn';
  upgradeLink.textContent = '立即升级';
  upgradeLink.onclick = function() {
    console.log('点击升级按钮');
    // 通过background.js代理打开新标签页
    chrome.runtime.sendMessage({
      action: 'openUpgradePage',
      url: 'http://localhost:8080/index.html#pricing'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('打开升级页面失败:', chrome.runtime.lastError.message);
        // 如果扩展API失败，尝试使用window.open作为备选方案
        window.open('http://localhost:8080/index.html#pricing', '_blank');
      } else {
        console.log('升级页面已打开');
      }
    });
  };
  
  // 组装消息气泡内容
  bubble.appendChild(textContentSpan);
  bubble.appendChild(upgradeLink);
  
  // 组装消息
  messageElement.appendChild(avatar);
  messageElement.appendChild(bubble);
  chatMessages.appendChild(messageElement);
  
  // 滚动到底部
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 打开升级页面
function openUpgradePage() {
  console.log('调用openUpgradePage函数');
  // 通过background.js代理打开新标签页
  chrome.runtime.sendMessage({
    action: 'openUpgradePage',
    url: 'http://localhost:8080/index.html#pricing'
  }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('打开升级页面失败:', chrome.runtime.lastError.message);
      // 如果扩展API失败，尝试使用window.open作为备选方案
      window.open('http://localhost:8080/index.html#pricing', '_blank');
    } else {
      console.log('升级页面已打开');
    }
  });
}

// 显示Premium功能详情
function showPremiumFeatures() {
  // 可以在这里添加更详细的功能介绍弹窗
  // 暂时使用alert，后续可以改为更美观的弹窗
  alert('Premium会员功能包括：\n\n' +
        '🤖 AI智能助手 - 基于视频内容的智能问答\n' +
        '📊 深度分析 - 视频内容结构化分析\n' +
        '🎯 个性化推荐 - 根据学习进度推荐内容\n' +
        '📚 学习记录 - 详细的学习历史和进度跟踪\n' +
        '🚀 优先支持 - 享受优先客服支持\n\n' +
        '立即升级享受完整的学习体验！');
}

/**
 * 消息显示
 */

// 获取用户头像URL
function getUserAvatarUrl(callback) {
  // 从Chrome存储中获取用户信息
  chrome.storage.sync.get(['userInfo'], function(result) {
    if (result.userInfo && result.userInfo.picture) {
      callback(result.userInfo.picture);
    } else {
      callback(null);
    }
  });
}

// 添加消息到聊天区域
function addMessage(chatContainer, message, sender, useTypewriter = false) {
  const chatMessages = chatContainer.querySelector('.chat-messages');
  
  // 创建消息元素
  const messageElement = document.createElement('div');
  messageElement.className = `chat-message ${sender}`;
  
  // 创建头像
  const avatar = document.createElement('div');
  avatar.className = `chat-avatar ${sender}`;
  
  if (sender === 'ai') {
    // 使用AI头像图片
    avatar.style.backgroundImage = `url(chrome-extension://${chrome.runtime.id}/assets/images/ai.png)`;
    avatar.style.backgroundSize = 'cover';
    avatar.style.backgroundPosition = 'center';
    avatar.style.backgroundRepeat = 'no-repeat';
  } else {
    // 使用用户的谷歌头像
    getUserAvatarUrl((avatarUrl) => {
      if (avatarUrl) {
        avatar.style.backgroundImage = `url(${avatarUrl})`;
        avatar.style.backgroundSize = 'cover';
        avatar.style.backgroundPosition = 'center';
        avatar.style.backgroundRepeat = 'no-repeat';
      } else {
        // 如果获取不到谷歌头像，使用默认头像
        avatar.style.backgroundImage = `url(chrome-extension://${chrome.runtime.id}/assets/images/user.png)`;
        avatar.style.backgroundSize = 'cover';
        avatar.style.backgroundPosition = 'center';
        avatar.style.backgroundRepeat = 'no-repeat';
      }
    });
  }
  
  // 创建消息气泡
  const bubble = document.createElement('div');
  bubble.className = 'chat-bubble';
  
  // 创建一个span来承载文本内容，确保复制按钮可以内联显示在文本末尾
  const textContentSpan = document.createElement('span');
  textContentSpan.className = 'chat-text-content'; // 可选：添加类名便于样式控制
  
  // 组装消息：头像 -> 消息元素 -> 聊天消息区域
  messageElement.appendChild(avatar);
  messageElement.appendChild(bubble);
  chatMessages.appendChild(messageElement);
  
  // 如果使用打字机效果
  if (useTypewriter) {
    bubble.appendChild(textContentSpan); // 先将文本内容的span添加到气泡中
    typeWriterEffectWithCopy(textContentSpan, message, sender); // 将span传递给打字机效果函数
  } else {
    // 直接设置文本内容
    textContentSpan.textContent = message;
    bubble.appendChild(textContentSpan); // 将文本内容的span添加到气泡中
    
    // 如果是AI消息，在文本末尾添加复制按钮
    if (sender === 'ai') {
      const copyButton = document.createElement('button');
      copyButton.className = 'copy-button';
      copyButton.innerHTML = '📋';
      copyButton.title = '复制回答';
      
      // 添加复制功能
      copyButton.addEventListener('click', function() {
        window.StudyModeUtils.copyToClipboard(message, copyButton);
      });
      
      bubble.appendChild(copyButton); // 将复制按钮添加到其父元素（即chat-bubble）中，紧随text-content-span之后
    }
  }
  
  // 滚动到底部
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 添加欢迎消息
function addWelcomeMessage(chatMessages) {
  const welcomeText = "您好！我是您的视频助手，我会基于当前视频的字幕内容回答您的问题。请随时向我提问关于视频内容的任何问题。";
  
  // 创建欢迎消息元素
  const messageElement = document.createElement('div');
  messageElement.className = 'chat-message ai';
  
  // 创建AI头像
  const avatar = document.createElement('div');
  avatar.className = 'chat-avatar ai';
  
  // 使用AI头像图片
  avatar.style.backgroundImage = `url(chrome-extension://${chrome.runtime.id}/assets/images/ai.png)`;
  avatar.style.backgroundSize = 'cover';
  avatar.style.backgroundPosition = 'center';
  avatar.style.backgroundRepeat = 'no-repeat';
  
  // 创建消息气泡
  const bubble = document.createElement('div');
  bubble.className = 'chat-bubble';
  
  // 组装消息
  messageElement.appendChild(avatar);
  messageElement.appendChild(bubble);
  chatMessages.appendChild(messageElement);
  
  // 使用打字机效果显示欢迎消息
  typeWriterEffect(bubble, welcomeText);
  
  // 滚动到底部
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * 加载消息处理
 */

// 添加loading消息
function addLoadingMessage(chatContainer) {
  const chatMessages = chatContainer.querySelector('.chat-messages');
  
  // 生成唯一ID
  const loadingId = 'loading-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  
  // 创建loading消息元素
  const messageElement = document.createElement('div');
  messageElement.className = 'chat-message ai loading-message';
  messageElement.id = loadingId;
  
  // 创建头像
  const avatar = document.createElement('div');
  avatar.className = 'chat-avatar ai';
  
  // 使用AI头像图片
  avatar.style.backgroundImage = `url(chrome-extension://${chrome.runtime.id}/assets/images/ai.png)`;
  avatar.style.backgroundSize = 'cover';
  avatar.style.backgroundPosition = 'center';
  avatar.style.backgroundRepeat = 'no-repeat';
  
  // 创建消息气泡
  const bubble = document.createElement('div');
  bubble.className = 'chat-bubble loading-bubble';
  
  // 创建loading动画和文字
  bubble.innerHTML = `
    <div class="loading-content">
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span class="loading-text">正在思考中...</span>
    </div>
  `;
  
  // 组装消息
  messageElement.appendChild(avatar);
  messageElement.appendChild(bubble);
  chatMessages.appendChild(messageElement);
  
  // 滚动到底部
  chatMessages.scrollTop = chatMessages.scrollHeight;
  
  return loadingId;
}

// 移除loading消息
function removeLoadingMessage(chatContainer, loadingMessageId) {
  const loadingMessage = document.getElementById(loadingMessageId);
  if (loadingMessage) {
    loadingMessage.remove();
  }
}

/**
 * 打字机效果
 */

// 带复制按钮的打字机效果 (不带光标)
function typeWriterEffectWithCopy(element, text, sender) {
  // element 现在是 textContentSpan
  element.innerHTML = ''; // 清空span内容
  
  let index = 0;
  
  function typeNext() {
    if (index < text.length) {
      // 将文字逐个添加到span中
      element.textContent += text[index];
      index++;
      
      // 随机延迟，模拟真实打字速度
      const delay = Math.random() * 50 + 30; // 30-80ms之间的随机延迟
      setTimeout(typeNext, delay);
    } else {
      // 打字完成
      // 如果是AI消息，在文本末尾添加复制按钮
      if (sender === 'ai') {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = '📋';
        copyButton.title = '复制回答';
        
        // 添加复制功能
        copyButton.addEventListener('click', function() {
          window.StudyModeUtils.copyToClipboard(text, copyButton);
        });
        
        // 将复制按钮添加到其父元素（即chat-bubble）中，紧随text-content-span之后
        element.parentNode.appendChild(copyButton);
      }
    }
  }
  
  typeNext();
}

// 普通的打字机效果 (用于欢迎消息，不带复制按钮)
function typeWriterEffect(element, text) {
  element.innerHTML = '';
  let index = 0;
  function type() {
    if (index < text.length) {
      element.textContent += text.charAt(index);
      index++;
      setTimeout(type, 30);
    }
  }
  type();
}

/**
 * 聊天区域清理
 */

// 清理聊天区域
function cleanupChatArea() {
  // 清理聊天气泡
  const chatBubble = document.getElementById('study-mode-chat-bubble');
  if (chatBubble) {
    chatBubble.remove();
  }
  
  const chatContainer = document.getElementById('study-mode-chat-container');
  if (chatContainer) {
    // 清空聊天消息
    const chatMessages = chatContainer.querySelector('.chat-messages');
    if (chatMessages) {
      chatMessages.innerHTML = '';
    }
    
    // 清空输入框
    const chatInput = chatContainer.querySelector('.chat-input');
    if (chatInput) {
      chatInput.value = '';
      chatInput.style.height = 'auto';
    }
    
    // 移除聊天容器
    chatContainer.remove();
    
    console.log('聊天区域已清理：气泡、消息记录、输入框内容已清空');
  }
}

/**
 * 视频切换处理
 */

// 为新视频重置聊天区域
function resetChatForNewVideo() {
  console.log('为新视频重置聊天区域');
  
  const chatContainer = document.getElementById('study-mode-chat-container');
  if (chatContainer) {
    // 清空聊天消息
    const chatMessages = chatContainer.querySelector('.chat-messages');
    if (chatMessages) {
      chatMessages.innerHTML = '';
      
      // 添加新的欢迎消息
      setTimeout(() => {
        addWelcomeMessage(chatMessages);
      }, 300);
    }
    
    // 清空输入框
    const chatInput = chatContainer.querySelector('.chat-input');
    if (chatInput) {
      chatInput.value = '';
      chatInput.style.height = 'auto';
    }
  }
  
  console.log('聊天区域已为新视频重置');
}

// 导出聊天模块功能，供其他模块使用
window.StudyModeChat = {
  // UI创建
  createChatArea,
  adjustChatContainerHeight,
  
  // 事件处理
  setupChatBubbleEvents,
  setupChatInputEvents,
  
  // 消息处理
  sendMessage,
  callChatAPI,
  
  // 消息显示
  addMessage,
  addWelcomeMessage,
  
  // 加载消息
  addLoadingMessage,
  removeLoadingMessage,
  
  // 打字机效果
  typeWriterEffectWithCopy,
  typeWriterEffect,
  
  // 清理功能
  cleanupChatArea,
  
  // 视频切换处理
  resetChatForNewVideo,

  // Premium会员升级提示
  addPremiumUpgradeMessage,
  openUpgradePage,
  showPremiumFeatures,
  
  // 权限检查（为了兼容性，也在chat模块中导出）
  checkPremiumPermission: window.StudyModeControls?.checkPremiumPermission,
  checkPlusOrPremiumPermission: window.StudyModeControls?.checkPlusOrPremiumPermission
}; 